"""
Utility functions for the ordinances app.
"""
import logging
from typing import Optional, Dict, Any, List
from django.core.cache import cache
from django.db.models import QuerySet
from django.http import HttpRequest
from django.core.paginator import Paginator, Page
from django.conf import settings

logger = logging.getLogger(__name__)


def get_client_ip(request: HttpRequest) -> str:
    """Get the client IP address from request."""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip


def paginate_queryset(
    queryset: QuerySet, 
    request: HttpRequest, 
    per_page: int = 12
) -> Page:
    """
    Paginate a queryset with error handling.
    
    Args:
        queryset: The queryset to paginate
        request: The HTTP request object
        per_page: Number of items per page
        
    Returns:
        Page object
    """
    paginator = Paginator(queryset, per_page)
    page_number = request.GET.get('page', 1)
    
    try:
        page_obj = paginator.get_page(page_number)
    except Exception as e:
        logger.warning(f"Pagination error: {e}")
        page_obj = paginator.get_page(1)
    
    return page_obj


def cache_key_for_user(user_id: Optional[int], key_suffix: str) -> str:
    """Generate a cache key for user-specific data."""
    user_part = f"user_{user_id}" if user_id else "anonymous"
    return f"{user_part}_{key_suffix}"


def get_or_set_cache(
    key: str, 
    callable_func, 
    timeout: int = 300,
    *args, 
    **kwargs
) -> Any:
    """
    Get value from cache or set it using the callable.
    
    Args:
        key: Cache key
        callable_func: Function to call if cache miss
        timeout: Cache timeout in seconds
        *args, **kwargs: Arguments for the callable
        
    Returns:
        Cached or computed value
    """
    value = cache.get(key)
    if value is None:
        try:
            value = callable_func(*args, **kwargs)
            cache.set(key, value, timeout)
        except Exception as e:
            logger.error(f"Error computing cache value for key {key}: {e}")
            return None
    return value


def sanitize_search_query(query: str) -> str:
    """
    Sanitize search query to prevent injection attacks.
    
    Args:
        query: Raw search query
        
    Returns:
        Sanitized query string
    """
    if not query:
        return ""
    
    # Remove potentially dangerous characters
    dangerous_chars = ['<', '>', '"', "'", '&', ';', '(', ')', '|']
    sanitized = query
    
    for char in dangerous_chars:
        sanitized = sanitized.replace(char, '')
    
    # Limit length
    sanitized = sanitized[:100]
    
    # Strip whitespace
    sanitized = sanitized.strip()
    
    return sanitized


def build_filter_context(request: HttpRequest) -> Dict[str, Any]:
    """
    Build context for filtering ordinances.
    
    Args:
        request: HTTP request object
        
    Returns:
        Dictionary with filter parameters
    """
    return {
        'search_query': sanitize_search_query(request.GET.get('search', '')),
        'category_slug': request.GET.get('category', ''),
        'year': request.GET.get('year', ''),
        'status': request.GET.get('status', ''),
    }


def log_user_action(
    user, 
    action: str, 
    ordinance=None, 
    request: HttpRequest = None,
    notes: str = ""
) -> None:
    """
    Log user actions for audit trail.
    
    Args:
        user: User who performed the action
        action: Description of the action
        ordinance: Related ordinance (optional)
        request: HTTP request object (optional)
        notes: Additional notes
    """
    try:
        from .models import OrdinanceLog
        
        ip_address = get_client_ip(request) if request else None
        
        OrdinanceLog.objects.create(
            ordinance=ordinance,
            user=user,
            action=action,
            notes=notes,
            ip_address=ip_address
        )
        
        logger.info(f"User action logged: {user} - {action}")
        
    except Exception as e:
        logger.error(f"Failed to log user action: {e}")


def validate_file_upload(uploaded_file) -> Dict[str, Any]:
    """
    Validate uploaded file for security and size constraints.
    
    Args:
        uploaded_file: Django UploadedFile object
        
    Returns:
        Dictionary with validation results
    """
    result = {
        'valid': True,
        'errors': [],
        'warnings': []
    }
    
    # Check file size (5MB limit)
    max_size = 5 * 1024 * 1024  # 5MB
    if uploaded_file.size > max_size:
        result['valid'] = False
        result['errors'].append(f"File size ({uploaded_file.size} bytes) exceeds maximum allowed size ({max_size} bytes)")
    
    # Check file extension
    allowed_extensions = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png']
    file_extension = uploaded_file.name.split('.')[-1].lower()
    
    if file_extension not in allowed_extensions:
        result['valid'] = False
        result['errors'].append(f"File type '{file_extension}' is not allowed. Allowed types: {', '.join(allowed_extensions)}")
    
    # Check for potentially dangerous filenames
    dangerous_patterns = ['..', '/', '\\', '<', '>', '|', ':', '*', '?', '"']
    for pattern in dangerous_patterns:
        if pattern in uploaded_file.name:
            result['valid'] = False
            result['errors'].append("Filename contains invalid characters")
            break
    
    return result


def generate_ordinance_number(year: int) -> str:
    """
    Generate the next ordinance number for a given year.
    
    Args:
        year: Year for the ordinance
        
    Returns:
        Generated ordinance number
    """
    from .models import Ordinance
    
    # Get the highest number for the year
    existing_ordinances = Ordinance.objects.filter(
        year_passed=year,
        ordinance_number__startswith=f"ORD-{year}-"
    ).order_by('-ordinance_number')
    
    if existing_ordinances.exists():
        last_number = existing_ordinances.first().ordinance_number
        # Extract the number part
        try:
            number_part = int(last_number.split('-')[-1])
            next_number = number_part + 1
        except (ValueError, IndexError):
            next_number = 1
    else:
        next_number = 1
    
    return f"ORD-{year}-{next_number:03d}"


def get_ordinance_statistics() -> Dict[str, int]:
    """
    Get comprehensive ordinance statistics.
    
    Returns:
        Dictionary with various statistics
    """
    from .models import Ordinance, Category, Sponsor
    from django.db.models import Count, Q
    
    cache_key = "ordinance_statistics"
    stats = cache.get(cache_key)
    
    if stats is None:
        stats = {
            'total_ordinances': Ordinance.objects.count(),
            'published_ordinances': Ordinance.objects.filter(status='published').count(),
            'draft_ordinances': Ordinance.objects.filter(status='draft').count(),
            'approved_ordinances': Ordinance.objects.filter(status='approved').count(),
            'total_categories': Category.objects.count(),
            'total_sponsors': Sponsor.objects.count(),
            'current_year_ordinances': Ordinance.objects.filter(
                year_passed=2024  # You might want to make this dynamic
            ).count(),
        }
        
        # Cache for 5 minutes
        cache.set(cache_key, stats, 300)
    
    return stats
