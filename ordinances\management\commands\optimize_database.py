"""
Management command to optimize database performance.
"""
from django.core.management.base import BaseCommand
from django.db import connection
from django.core.cache import cache
from django.utils import timezone
from datetime import timedelta

from ordinances.models import Ordinance, OrdinanceLog, Attachment


class Command(BaseCommand):
    help = 'Optimize database performance and clean up old data'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be done without making changes',
        )
        parser.add_argument(
            '--clean-logs',
            action='store_true',
            help='Clean up old log entries (older than 1 year)',
        )
        parser.add_argument(
            '--vacuum',
            action='store_true',
            help='Run database vacuum (PostgreSQL only)',
        )
        parser.add_argument(
            '--analyze',
            action='store_true',
            help='Update database statistics',
        )

    def handle(self, *args, **options):
        self.dry_run = options['dry_run']
        
        if self.dry_run:
            self.stdout.write(
                self.style.WARNING('DRY RUN MODE - No changes will be made')
            )
        
        # Clear cache
        self.clear_cache()
        
        # Clean up old logs
        if options['clean_logs']:
            self.clean_old_logs()
        
        # Database maintenance
        if options['vacuum']:
            self.vacuum_database()
        
        if options['analyze']:
            self.analyze_database()
        
        # Generate database statistics
        self.show_database_stats()
        
        self.stdout.write(
            self.style.SUCCESS('Database optimization completed!')
        )

    def clear_cache(self):
        """Clear all cache entries."""
        self.stdout.write('Clearing cache...')
        
        if not self.dry_run:
            cache.clear()
        
        self.stdout.write(
            self.style.SUCCESS('✓ Cache cleared')
        )

    def clean_old_logs(self):
        """Clean up old log entries."""
        self.stdout.write('Cleaning up old log entries...')
        
        # Delete logs older than 1 year
        cutoff_date = timezone.now() - timedelta(days=365)
        old_logs = OrdinanceLog.objects.filter(timestamp__lt=cutoff_date)
        count = old_logs.count()
        
        if count > 0:
            self.stdout.write(f'Found {count} old log entries to delete')
            
            if not self.dry_run:
                deleted_count, _ = old_logs.delete()
                self.stdout.write(
                    self.style.SUCCESS(f'✓ Deleted {deleted_count} old log entries')
                )
            else:
                self.stdout.write(f'Would delete {count} old log entries')
        else:
            self.stdout.write('No old log entries found')

    def vacuum_database(self):
        """Run database vacuum (PostgreSQL only)."""
        self.stdout.write('Running database vacuum...')
        
        if 'postgresql' not in connection.settings_dict['ENGINE']:
            self.stdout.write(
                self.style.WARNING('Vacuum is only supported for PostgreSQL')
            )
            return
        
        if not self.dry_run:
            with connection.cursor() as cursor:
                cursor.execute('VACUUM ANALYZE;')
            
            self.stdout.write(
                self.style.SUCCESS('✓ Database vacuum completed')
            )
        else:
            self.stdout.write('Would run database vacuum')

    def analyze_database(self):
        """Update database statistics."""
        self.stdout.write('Updating database statistics...')
        
        if not self.dry_run:
            with connection.cursor() as cursor:
                if 'postgresql' in connection.settings_dict['ENGINE']:
                    cursor.execute('ANALYZE;')
                elif 'mysql' in connection.settings_dict['ENGINE']:
                    # Get all tables
                    cursor.execute("""
                        SELECT table_name 
                        FROM information_schema.tables 
                        WHERE table_schema = DATABASE()
                    """)
                    tables = cursor.fetchall()
                    
                    for table in tables:
                        cursor.execute(f'ANALYZE TABLE {table[0]};')
                elif 'sqlite' in connection.settings_dict['ENGINE']:
                    cursor.execute('ANALYZE;')
            
            self.stdout.write(
                self.style.SUCCESS('✓ Database statistics updated')
            )
        else:
            self.stdout.write('Would update database statistics')

    def show_database_stats(self):
        """Show database statistics."""
        self.stdout.write('\n' + '='*50)
        self.stdout.write('DATABASE STATISTICS')
        self.stdout.write('='*50)
        
        # Count records
        ordinance_count = Ordinance.objects.count()
        published_count = Ordinance.objects.filter(status='published').count()
        draft_count = Ordinance.objects.filter(status='draft').count()
        log_count = OrdinanceLog.objects.count()
        attachment_count = Attachment.objects.count()
        
        self.stdout.write(f'Total Ordinances: {ordinance_count}')
        self.stdout.write(f'Published Ordinances: {published_count}')
        self.stdout.write(f'Draft Ordinances: {draft_count}')
        self.stdout.write(f'Log Entries: {log_count}')
        self.stdout.write(f'Attachments: {attachment_count}')
        
        # Database size (PostgreSQL only)
        if 'postgresql' in connection.settings_dict['ENGINE']:
            with connection.cursor() as cursor:
                cursor.execute("""
                    SELECT pg_size_pretty(pg_database_size(current_database()))
                """)
                db_size = cursor.fetchone()[0]
                self.stdout.write(f'Database Size: {db_size}')
        
        # Show largest tables
        self.show_table_sizes()

    def show_table_sizes(self):
        """Show table sizes."""
        self.stdout.write('\nTABLE SIZES:')
        self.stdout.write('-' * 30)
        
        with connection.cursor() as cursor:
            if 'postgresql' in connection.settings_dict['ENGINE']:
                cursor.execute("""
                    SELECT 
                        schemaname,
                        tablename,
                        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size
                    FROM pg_tables 
                    WHERE schemaname = 'public'
                    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC
                    LIMIT 10
                """)
                
                for row in cursor.fetchall():
                    self.stdout.write(f'{row[1]}: {row[2]}')
                    
            elif 'mysql' in connection.settings_dict['ENGINE']:
                cursor.execute("""
                    SELECT 
                        table_name,
                        ROUND(((data_length + index_length) / 1024 / 1024), 2) AS size_mb
                    FROM information_schema.TABLES 
                    WHERE table_schema = DATABASE()
                    ORDER BY (data_length + index_length) DESC
                    LIMIT 10
                """)
                
                for row in cursor.fetchall():
                    self.stdout.write(f'{row[0]}: {row[1]} MB')
                    
            elif 'sqlite' in connection.settings_dict['ENGINE']:
                # SQLite doesn't have built-in table size queries
                self.stdout.write('Table size information not available for SQLite')

    def check_indexes(self):
        """Check for missing indexes."""
        self.stdout.write('\nCHECKING INDEXES:')
        self.stdout.write('-' * 30)
        
        # This is a simplified check - in production you'd want more sophisticated analysis
        with connection.cursor() as cursor:
            if 'postgresql' in connection.settings_dict['ENGINE']:
                cursor.execute("""
                    SELECT 
                        schemaname,
                        tablename,
                        attname,
                        n_distinct,
                        correlation
                    FROM pg_stats 
                    WHERE schemaname = 'public'
                    AND n_distinct > 100
                    ORDER BY n_distinct DESC
                """)
                
                self.stdout.write('Columns that might benefit from indexes:')
                for row in cursor.fetchall():
                    self.stdout.write(f'{row[1]}.{row[2]} (distinct values: {row[3]})')
            else:
                self.stdout.write('Index analysis not available for this database engine')
