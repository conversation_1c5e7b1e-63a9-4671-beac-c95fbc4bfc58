import logging
from typing import Optional, List
from django.db import models
from django.utils.text import slugify
from django.contrib.auth import get_user_model
from django.urls import reverse
from django.db.models import Q, QuerySet
from django.core.validators import MinLengthValidator, FileExtensionValidator
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.core.cache import cache

User = get_user_model()
logger = logging.getLogger(__name__)


class CategoryQuerySet(models.QuerySet):
    """Custom QuerySet for Category model."""

    def with_ordinance_counts(self):
        """Annotate categories with ordinance counts."""
        return self.annotate(
            total_ordinances=models.Count('ordinances'),
            published_ordinances=models.Count(
                'ordinances',
                filter=Q(ordinances__status='published')
            )
        )

    def active(self):
        """Return categories that have at least one ordinance."""
        return self.filter(ordinances__isnull=False).distinct()


class Category(models.Model):
    name = models.CharField(
        max_length=100,
        unique=True,
        validators=[MinLengthValidator(2)],
        help_text="Category name (2-100 characters)"
    )
    slug = models.SlugField(max_length=120, unique=True, blank=True)
    description = models.TextField(
        blank=True,
        help_text="Optional description of the category"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = CategoryQuerySet.as_manager()

    class Meta:
        verbose_name_plural = "Categories"
        ordering = ['name']
        indexes = [
            models.Index(fields=['name']),
            models.Index(fields=['slug']),
        ]

    def __str__(self) -> str:
        return self.name

    def clean(self):
        """Validate the model."""
        super().clean()
        if self.name:
            self.name = self.name.strip().title()

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(self.name)
        self.full_clean()
        super().save(*args, **kwargs)
        # Clear cache when category is saved
        cache.delete('categories_list')

    def get_ordinance_count(self) -> int:
        """Get the number of ordinances in this category."""
        return self.ordinances.filter(status__in=['approved', 'published']).count()


class SponsorQuerySet(models.QuerySet):
    """Custom QuerySet for Sponsor model."""

    def active(self):
        """Return active sponsors."""
        return self.filter(is_active=True)

    def by_position(self, position: str):
        """Filter sponsors by position."""
        return self.filter(position__icontains=position)


class Sponsor(models.Model):
    name = models.CharField(
        max_length=255,
        validators=[MinLengthValidator(2)],
        help_text="Full name of the sponsor"
    )
    position = models.CharField(
        max_length=100,
        help_text="Official position or title"
    )
    is_active = models.BooleanField(
        default=True,
        help_text="Whether this sponsor is currently active"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    objects = SponsorQuerySet.as_manager()

    class Meta:
        ordering = ['position', 'name']
        indexes = [
            models.Index(fields=['position']),
            models.Index(fields=['is_active']),
        ]

    def __str__(self) -> str:
        return f"{self.name} ({self.position})"

    def clean(self):
        """Validate the model."""
        super().clean()
        if self.name:
            self.name = self.name.strip().title()
        if self.position:
            self.position = self.position.strip().title()


class OrdinanceQuerySet(models.QuerySet):
    """Custom QuerySet for Ordinance model."""

    def published(self):
        """Return published ordinances."""
        return self.filter(status='published')

    def public(self):
        """Return publicly viewable ordinances."""
        return self.filter(status__in=['approved', 'published'])

    def by_year(self, year: int):
        """Filter ordinances by year."""
        return self.filter(year_passed=year)

    def by_category(self, category_slug: str):
        """Filter ordinances by category slug."""
        return self.filter(category__slug=category_slug)

    def search(self, query: str):
        """Search ordinances by title, content, or ordinance number."""
        if not query:
            return self.none()
        return self.filter(
            Q(title__icontains=query) |
            Q(content__icontains=query) |
            Q(ordinance_number__icontains=query)
        ).distinct()

    def with_related(self):
        """Optimize queries by selecting related objects."""
        return self.select_related('category', 'created_by', 'updated_by').prefetch_related('sponsors')


class Ordinance(models.Model):
    STATUS_CHOICES = [
        ("draft", "Draft"),
        ("reviewed", "Reviewed"),
        ("approved", "Approved"),
        ("published", "Published"),
    ]

    ordinance_number = models.CharField(
        max_length=100,
        unique=True,
        validators=[MinLengthValidator(3)],
        help_text="Unique ordinance number (e.g., ORD-2024-001)"
    )
    title = models.CharField(
        max_length=255,
        validators=[MinLengthValidator(10)],
        help_text="Descriptive title of the ordinance"
    )
    slug = models.SlugField(max_length=300, unique=True, blank=True)
    content = models.TextField(
        validators=[MinLengthValidator(50)],
        help_text="Full content of the ordinance"
    )
    category = models.ForeignKey(
        Category,
        on_delete=models.SET_NULL,
        null=True,
        related_name='ordinances',
        help_text="Category this ordinance belongs to"
    )
    year_passed = models.PositiveIntegerField(
        help_text="Year the ordinance was passed"
    )
    status = models.CharField(
        max_length=20,
        choices=STATUS_CHOICES,
        default="draft",
        db_index=True
    )
    sponsors = models.ManyToManyField(
        Sponsor,
        blank=True,
        help_text="Council members who sponsored this ordinance"
    )
    created_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="created_ordinances"
    )
    updated_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="updated_ordinances"
    )
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    is_archived = models.BooleanField(
        default=False,
        help_text="Whether this ordinance is archived"
    )

    objects = OrdinanceQuerySet.as_manager()

    class Meta:
        ordering = ['-year_passed', 'ordinance_number']
        indexes = [
            models.Index(fields=['status']),
            models.Index(fields=['year_passed']),
            models.Index(fields=['category']),
            models.Index(fields=['slug']),
            models.Index(fields=['created_at']),
            models.Index(fields=['is_archived']),
        ]
        constraints = [
            models.CheckConstraint(
                check=models.Q(year_passed__gte=1900) & models.Q(year_passed__lte=2100),
                name='valid_year_range'
            ),
        ]

    def __str__(self) -> str:
        return f"{self.ordinance_number} - {self.title}"

    def get_absolute_url(self) -> str:
        return reverse('ordinances:ordinance_detail', kwargs={'slug': self.slug})

    def clean(self):
        """Validate the model."""
        super().clean()

        # Validate year
        current_year = timezone.now().year
        if self.year_passed and (self.year_passed < 1900 or self.year_passed > current_year + 1):
            raise ValidationError({
                'year_passed': f'Year must be between 1900 and {current_year + 1}'
            })

        # Clean text fields
        if self.title:
            self.title = self.title.strip()
        if self.ordinance_number:
            self.ordinance_number = self.ordinance_number.strip().upper()

    def save(self, *args, **kwargs):
        if not self.slug:
            self.slug = slugify(f"{self.ordinance_number}-{self.title}")
        self.full_clean()
        super().save(*args, **kwargs)

        # Clear related caches
        cache.delete('recent_ordinances')
        cache.delete(f'ordinance_{self.slug}')

        # Log the save action
        logger.info(f"Ordinance {self.ordinance_number} saved with status {self.status}")

    def is_public(self) -> bool:
        """Check if ordinance is publicly viewable."""
        return self.status in ['approved', 'published'] and not self.is_archived

    def can_edit(self, user) -> bool:
        """Check if user can edit this ordinance."""
        if not user or not user.is_authenticated:
            return False
        return user.is_staff or user == self.created_by

    def get_status_color(self) -> str:
        """Get CSS color class for status."""
        colors = {
            'draft': 'gray',
            'reviewed': 'blue',
            'approved': 'yellow',
            'published': 'green',
        }
        return colors.get(self.status, 'gray')


def attachment_upload_path(instance, filename: str) -> str:
    """Generate upload path for attachments."""
    import os
    from django.utils import timezone

    # Get file extension
    ext = os.path.splitext(filename)[1]
    # Create new filename with timestamp
    new_filename = f"{instance.ordinance.ordinance_number}_{timezone.now().strftime('%Y%m%d_%H%M%S')}{ext}"
    return f"attachments/{instance.ordinance.year_passed}/{new_filename}"


class Attachment(models.Model):
    ALLOWED_EXTENSIONS = ['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png']

    ordinance = models.ForeignKey(
        Ordinance,
        on_delete=models.CASCADE,
        related_name="attachments"
    )
    file = models.FileField(
        upload_to=attachment_upload_path,
        validators=[
            FileExtensionValidator(allowed_extensions=ALLOWED_EXTENSIONS)
        ],
        help_text=f"Allowed file types: {', '.join(ALLOWED_EXTENSIONS)}"
    )
    description = models.CharField(
        max_length=255,
        blank=True,
        help_text="Optional description of the attachment"
    )
    file_size = models.PositiveIntegerField(
        null=True,
        blank=True,
        help_text="File size in bytes"
    )
    uploaded_at = models.DateTimeField(auto_now_add=True)
    uploaded_by = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True,
        related_name="uploaded_attachments"
    )

    class Meta:
        ordering = ['-uploaded_at']
        indexes = [
            models.Index(fields=['ordinance', 'uploaded_at']),
        ]

    def __str__(self) -> str:
        return f"Attachment for {self.ordinance.ordinance_number}"

    def clean(self):
        """Validate the attachment."""
        super().clean()

        if self.file:
            # Check file size (5MB limit)
            if self.file.size > 5 * 1024 * 1024:
                raise ValidationError({
                    'file': 'File size cannot exceed 5MB'
                })

            # Store file size
            self.file_size = self.file.size

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)

    def get_file_size_display(self) -> str:
        """Return human-readable file size."""
        if not self.file_size:
            return "Unknown"

        for unit in ['B', 'KB', 'MB', 'GB']:
            if self.file_size < 1024.0:
                return f"{self.file_size:.1f} {unit}"
            self.file_size /= 1024.0
        return f"{self.file_size:.1f} TB"


class OrdinanceLog(models.Model):
    """Audit log for ordinance changes."""

    ordinance = models.ForeignKey(
        Ordinance,
        on_delete=models.CASCADE,
        related_name="logs"
    )
    user = models.ForeignKey(
        User,
        on_delete=models.SET_NULL,
        null=True
    )
    action = models.CharField(
        max_length=255,
        help_text="Description of the action performed"
    )
    timestamp = models.DateTimeField(auto_now_add=True)
    notes = models.TextField(
        blank=True,
        help_text="Additional notes about the action"
    )
    ip_address = models.GenericIPAddressField(
        null=True,
        blank=True,
        help_text="IP address of the user who performed the action"
    )

    class Meta:
        ordering = ['-timestamp']
        indexes = [
            models.Index(fields=['ordinance', 'timestamp']),
            models.Index(fields=['user', 'timestamp']),
            models.Index(fields=['action']),
        ]

    def __str__(self) -> str:
        user_name = self.user.username if self.user else "System"
        return f"{user_name} {self.action} on {self.ordinance.ordinance_number}"


class Official(models.Model):
    POSITION_CHOICES = [
        ('mayor', 'Municipal Mayor'),
        ('vice_mayor', 'Vice Mayor'),
        ('councilor', 'Sangguniang Bayan Member'),
        ('secretary', 'Municipal Secretary'),
        ('treasurer', 'Municipal Treasurer'),
    ]

    STATUS_CHOICES = [
        ('active', 'Active'),
        ('inactive', 'Inactive'),
        ('retired', 'Retired'),
    ]

    name = models.CharField(max_length=200)
    position = models.CharField(max_length=50, choices=POSITION_CHOICES)
    committee = models.CharField(max_length=200, blank=True, help_text="Committee or department headed")
    profile_picture = models.ImageField(upload_to='officials/', blank=True, null=True)
    bio = models.TextField(blank=True, help_text="Brief biography or description")
    achievements = models.TextField(blank=True, help_text="Key achievements (one per line)")
    term_start = models.DateField(help_text="Start of current term")
    term_end = models.DateField(help_text="End of current term")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='active')
    order = models.PositiveIntegerField(default=0, help_text="Display order")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        ordering = ['order', 'position', 'name']

    def __str__(self):
        return f"{self.name} - {self.get_position_display()}"

    def get_achievements_list(self):
        """Return achievements as a list"""
        if self.achievements:
            return [achievement.strip() for achievement in self.achievements.split('\n') if achievement.strip()]
        return []

    def get_profile_picture_url(self):
        """Return profile picture URL or default placeholder"""
        if self.profile_picture:
            return self.profile_picture.url
        return None

    @property
    def is_mayor(self):
        return self.position == 'mayor'

    @property
    def is_vice_mayor(self):
        return self.position == 'vice_mayor'

    @property
    def is_councilor(self):
        return self.position == 'councilor'
