/* Custom styles for the Sangguniang Bayan Ordinance System */

/* Line clamp utilities for text truncation */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Print styles */
@media print {
    .no-print {
        display: none !important;
    }
    
    .print-break-before {
        page-break-before: always;
    }
    
    .print-break-after {
        page-break-after: always;
    }
    
    /* Hide navigation and footer when printing */
    nav, footer, .htmx-indicator {
        display: none !important;
    }
    
    /* Ensure content is readable when printed */
    body {
        font-size: 12pt;
        line-height: 1.4;
        color: black;
        background: white;
    }
    
    /* Expand content for print */
    .max-w-4xl, .max-w-7xl {
        max-width: none;
        margin: 0;
        padding: 0;
    }
}

/* HTMX loading states */
.htmx-request .htmx-indicator {
    opacity: 1;
}

.htmx-request.htmx-indicator {
    opacity: 1;
}

/* Smooth transitions for interactive elements */
.transition-all {
    transition: all 0.2s ease-in-out;
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a1a1a1;
}

/* Focus styles for accessibility */
.focus-ring:focus {
    outline: 2px solid #3b82f6;
    outline-offset: 2px;
}

/* Custom button hover effects */
.btn-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Status badge animations */
.status-badge {
    transition: all 0.2s ease-in-out;
}

.status-badge:hover {
    transform: scale(1.05);
}

/* Card hover effects */
.card-hover:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

/* Loading spinner animation */
@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

.animate-spin {
    animation: spin 1s linear infinite;
}

/* Fade in animation for content */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-out;
}

/* Custom dropdown animations */
.dropdown-enter {
    opacity: 0;
    transform: scale(0.95);
}

.dropdown-enter-active {
    opacity: 1;
    transform: scale(1);
    transition: all 0.1s ease-out;
}

.dropdown-exit {
    opacity: 1;
    transform: scale(1);
}

.dropdown-exit-active {
    opacity: 0;
    transform: scale(0.95);
    transition: all 0.1s ease-in;
}

/* Responsive text sizes */
@media (max-width: 640px) {
    .responsive-text-lg {
        font-size: 1rem;
        line-height: 1.5rem;
    }
    
    .responsive-text-xl {
        font-size: 1.125rem;
        line-height: 1.75rem;
    }
    
    .responsive-text-2xl {
        font-size: 1.25rem;
        line-height: 1.75rem;
    }
    
    .responsive-text-3xl {
        font-size: 1.5rem;
        line-height: 2rem;
    }
}

/* Custom form styles */
.form-input:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select:focus {
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Table responsive wrapper */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
}

/* Custom alert styles */
.alert {
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
}

.alert-success {
    background-color: #d1fae5;
    border-color: #a7f3d0;
    color: #065f46;
}

.alert-error {
    background-color: #fee2e2;
    border-color: #fecaca;
    color: #991b1b;
}

.alert-warning {
    background-color: #fef3c7;
    border-color: #fde68a;
    color: #92400e;
}

.alert-info {
    background-color: #dbeafe;
    border-color: #bfdbfe;
    color: #1e40af;
}

/* Utility classes */
.text-shadow {
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.backdrop-blur {
    backdrop-filter: blur(4px);
}

/* Mobile menu improvements */
@media (max-width: 768px) {
    .mobile-menu {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 50;
    }
    
    .mobile-menu-content {
        background: white;
        height: 100%;
        width: 80%;
        max-width: 300px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }
}
