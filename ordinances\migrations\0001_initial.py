# Generated by Django 4.2.21 on 2025-05-25 04:00

from django.conf import settings
import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import ordinances.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Attachment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file', models.FileField(help_text='Allowed file types: pdf, doc, docx, txt, jpg, jpeg, png', upload_to=ordinances.models.attachment_upload_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['pdf', 'doc', 'docx', 'txt', 'jpg', 'jpeg', 'png'])])),
                ('description', models.Char<PERSON>ield(blank=True, help_text='Optional description of the attachment', max_length=255)),
                ('file_size', models.PositiveIntegerField(blank=True, help_text='File size in bytes', null=True)),
                ('uploaded_at', models.DateTimeField(auto_now_add=True)),
            ],
            options={
                'ordering': ['-uploaded_at'],
            },
        ),
        migrations.CreateModel(
            name='Category',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Category name (2-100 characters)', max_length=100, unique=True, validators=[django.core.validators.MinLengthValidator(2)])),
                ('slug', models.SlugField(blank=True, max_length=120, unique=True)),
                ('description', models.TextField(blank=True, help_text='Optional description of the category')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name_plural': 'Categories',
                'ordering': ['name'],
            },
        ),
        migrations.CreateModel(
            name='Official',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=200)),
                ('position', models.CharField(choices=[('mayor', 'Municipal Mayor'), ('vice_mayor', 'Vice Mayor'), ('councilor', 'Sangguniang Bayan Member'), ('secretary', 'Municipal Secretary'), ('treasurer', 'Municipal Treasurer')], max_length=50)),
                ('committee', models.CharField(blank=True, help_text='Committee or department headed', max_length=200)),
                ('profile_picture', models.ImageField(blank=True, null=True, upload_to='officials/')),
                ('bio', models.TextField(blank=True, help_text='Brief biography or description')),
                ('achievements', models.TextField(blank=True, help_text='Key achievements (one per line)')),
                ('term_start', models.DateField(help_text='Start of current term')),
                ('term_end', models.DateField(help_text='End of current term')),
                ('status', models.CharField(choices=[('active', 'Active'), ('inactive', 'Inactive'), ('retired', 'Retired')], default='active', max_length=20)),
                ('order', models.PositiveIntegerField(default=0, help_text='Display order')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['order', 'position', 'name'],
            },
        ),
        migrations.CreateModel(
            name='Ordinance',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('ordinance_number', models.CharField(help_text='Unique ordinance number (e.g., ORD-2024-001)', max_length=100, unique=True, validators=[django.core.validators.MinLengthValidator(3)])),
                ('title', models.CharField(help_text='Descriptive title of the ordinance', max_length=255, validators=[django.core.validators.MinLengthValidator(10)])),
                ('slug', models.SlugField(blank=True, max_length=300, unique=True)),
                ('content', models.TextField(help_text='Full content of the ordinance', validators=[django.core.validators.MinLengthValidator(50)])),
                ('year_passed', models.PositiveIntegerField(help_text='Year the ordinance was passed')),
                ('status', models.CharField(choices=[('draft', 'Draft'), ('reviewed', 'Reviewed'), ('approved', 'Approved'), ('published', 'Published')], db_index=True, default='draft', max_length=20)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('is_archived', models.BooleanField(default=False, help_text='Whether this ordinance is archived')),
                ('category', models.ForeignKey(help_text='Category this ordinance belongs to', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='ordinances', to='ordinances.category')),
                ('created_by', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_ordinances', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-year_passed', 'ordinance_number'],
            },
        ),
        migrations.CreateModel(
            name='Sponsor',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='Full name of the sponsor', max_length=255, validators=[django.core.validators.MinLengthValidator(2)])),
                ('position', models.CharField(help_text='Official position or title', max_length=100)),
                ('is_active', models.BooleanField(default=True, help_text='Whether this sponsor is currently active')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'ordering': ['position', 'name'],
                'indexes': [models.Index(fields=['position'], name='ordinances__positio_ba01cb_idx'), models.Index(fields=['is_active'], name='ordinances__is_acti_000226_idx')],
            },
        ),
        migrations.CreateModel(
            name='OrdinanceLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action', models.CharField(help_text='Description of the action performed', max_length=255)),
                ('timestamp', models.DateTimeField(auto_now_add=True)),
                ('notes', models.TextField(blank=True, help_text='Additional notes about the action')),
                ('ip_address', models.GenericIPAddressField(blank=True, help_text='IP address of the user who performed the action', null=True)),
                ('ordinance', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='logs', to='ordinances.ordinance')),
                ('user', models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['-timestamp'],
            },
        ),
        migrations.AddField(
            model_name='ordinance',
            name='sponsors',
            field=models.ManyToManyField(blank=True, help_text='Council members who sponsored this ordinance', to='ordinances.sponsor'),
        ),
        migrations.AddField(
            model_name='ordinance',
            name='updated_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='updated_ordinances', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['name'], name='ordinances__name_c677e7_idx'),
        ),
        migrations.AddIndex(
            model_name='category',
            index=models.Index(fields=['slug'], name='ordinances__slug_6e9cb8_idx'),
        ),
        migrations.AddField(
            model_name='attachment',
            name='ordinance',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='attachments', to='ordinances.ordinance'),
        ),
        migrations.AddField(
            model_name='attachment',
            name='uploaded_by',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='uploaded_attachments', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='ordinancelog',
            index=models.Index(fields=['ordinance', 'timestamp'], name='ordinances__ordinan_18a6f1_idx'),
        ),
        migrations.AddIndex(
            model_name='ordinancelog',
            index=models.Index(fields=['user', 'timestamp'], name='ordinances__user_id_4ce5cd_idx'),
        ),
        migrations.AddIndex(
            model_name='ordinancelog',
            index=models.Index(fields=['action'], name='ordinances__action_5100cb_idx'),
        ),
        migrations.AddIndex(
            model_name='ordinance',
            index=models.Index(fields=['status'], name='ordinances__status_19dabb_idx'),
        ),
        migrations.AddIndex(
            model_name='ordinance',
            index=models.Index(fields=['year_passed'], name='ordinances__year_pa_c412d3_idx'),
        ),
        migrations.AddIndex(
            model_name='ordinance',
            index=models.Index(fields=['category'], name='ordinances__categor_9cbb2b_idx'),
        ),
        migrations.AddIndex(
            model_name='ordinance',
            index=models.Index(fields=['slug'], name='ordinances__slug_d02240_idx'),
        ),
        migrations.AddIndex(
            model_name='ordinance',
            index=models.Index(fields=['created_at'], name='ordinances__created_c86920_idx'),
        ),
        migrations.AddIndex(
            model_name='ordinance',
            index=models.Index(fields=['is_archived'], name='ordinances__is_arch_05f7c0_idx'),
        ),
        migrations.AddConstraint(
            model_name='ordinance',
            constraint=models.CheckConstraint(check=models.Q(('year_passed__gte', 1900), ('year_passed__lte', 2100)), name='valid_year_range'),
        ),
        migrations.AddIndex(
            model_name='attachment',
            index=models.Index(fields=['ordinance', 'uploaded_at'], name='ordinances__ordinan_888ffd_idx'),
        ),
    ]
