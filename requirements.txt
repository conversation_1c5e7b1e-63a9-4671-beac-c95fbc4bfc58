# Core Django
Django>=4.2,<5.0

# Image processing
Pillow>=10.0.0

# PDF generation
reportlab>=4.0.0

# Environment variables
python-decouple>=3.8

# Database drivers (uncomment as needed)
# psycopg2-binary>=2.9.0  # PostgreSQL
# mysqlclient>=2.1.0      # MySQL

# Performance & Caching
django-redis>=5.3.0

# Development tools
django-debug-toolbar>=4.2.0
django-extensions>=3.2.0

# Testing
pytest>=7.4.0
pytest-django>=4.5.0
pytest-cov>=4.1.0
factory-boy>=3.3.0

# Code quality
black>=23.0.0
flake8>=6.0.0
isort>=5.12.0
mypy>=1.5.0
django-stubs>=4.2.0

# Security
django-ratelimit>=4.1.0
django-csp>=3.7

# Production
gunicorn>=21.2.0
whitenoise>=6.5.0
