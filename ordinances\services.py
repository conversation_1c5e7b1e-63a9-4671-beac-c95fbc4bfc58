"""
Service layer for ordinances app.
Contains business logic separated from views.
"""
import logging
from typing import Optional, Dict, Any, List, Tuple
from django.db.models import QuerySet, Q, Count
from django.core.cache import cache
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from django.utils import timezone

from .models import Ordinance, Category, Sponsor, OrdinanceLog, Official
from .utils import sanitize_search_query, log_user_action, get_ordinance_statistics

User = get_user_model()
logger = logging.getLogger(__name__)


class OrdinanceService:
    """Service class for ordinance-related operations."""
    
    @staticmethod
    def get_public_ordinances() -> QuerySet:
        """Get all publicly viewable ordinances with optimized queries."""
        return Ordinance.objects.public().with_related().order_by('-year_passed', 'ordinance_number')
    
    @staticmethod
    def search_ordinances(query: str, filters: Dict[str, Any] = None) -> QuerySet:
        """
        Search ordinances with filters.
        
        Args:
            query: Search query string
            filters: Additional filters (category, year, status)
            
        Returns:
            Filtered QuerySet
        """
        ordinances = Ordinance.objects.public().with_related()
        
        # Apply search query
        if query:
            sanitized_query = sanitize_search_query(query)
            ordinances = ordinances.search(sanitized_query)
        
        # Apply filters
        if filters:
            if filters.get('category_slug'):
                ordinances = ordinances.by_category(filters['category_slug'])
            
            if filters.get('year'):
                try:
                    year = int(filters['year'])
                    ordinances = ordinances.by_year(year)
                except (ValueError, TypeError):
                    pass
            
            if filters.get('status') and filters['status'] in ['approved', 'published']:
                ordinances = ordinances.filter(status=filters['status'])
        
        return ordinances.distinct()
    
    @staticmethod
    def get_ordinance_by_slug(slug: str) -> Optional[Ordinance]:
        """
        Get ordinance by slug with caching.
        
        Args:
            slug: Ordinance slug
            
        Returns:
            Ordinance instance or None
        """
        cache_key = f"ordinance_{slug}"
        ordinance = cache.get(cache_key)
        
        if ordinance is None:
            try:
                ordinance = Ordinance.objects.public().with_related().get(slug=slug)
                cache.set(cache_key, ordinance, 300)  # Cache for 5 minutes
            except Ordinance.DoesNotExist:
                return None
        
        return ordinance
    
    @staticmethod
    def get_related_ordinances(ordinance: Ordinance, limit: int = 3) -> QuerySet:
        """
        Get ordinances related to the given ordinance.
        
        Args:
            ordinance: Base ordinance
            limit: Maximum number of related ordinances
            
        Returns:
            QuerySet of related ordinances
        """
        if not ordinance.category:
            return Ordinance.objects.none()
        
        return Ordinance.objects.public().filter(
            category=ordinance.category
        ).exclude(id=ordinance.id).with_related()[:limit]
    
    @staticmethod
    def create_ordinance(data: Dict[str, Any], user) -> Tuple[Ordinance, List[str]]:
        """
        Create a new ordinance with validation.
        
        Args:
            data: Ordinance data
            user: User creating the ordinance
            
        Returns:
            Tuple of (ordinance, errors)
        """
        errors = []
        
        try:
            # Validate required fields
            required_fields = ['ordinance_number', 'title', 'content', 'year_passed']
            for field in required_fields:
                if not data.get(field):
                    errors.append(f"{field.replace('_', ' ').title()} is required")
            
            if errors:
                return None, errors
            
            # Create ordinance
            ordinance = Ordinance(
                ordinance_number=data['ordinance_number'],
                title=data['title'],
                content=data['content'],
                year_passed=int(data['year_passed']),
                status=data.get('status', 'draft'),
                created_by=user,
                updated_by=user
            )
            
            # Set category if provided
            if data.get('category_id'):
                try:
                    category = Category.objects.get(id=data['category_id'])
                    ordinance.category = category
                except Category.DoesNotExist:
                    errors.append("Invalid category selected")
            
            if errors:
                return None, errors
            
            # Save ordinance
            ordinance.full_clean()
            ordinance.save()
            
            # Add sponsors
            if data.get('sponsor_ids'):
                sponsors = Sponsor.objects.filter(id__in=data['sponsor_ids'])
                ordinance.sponsors.set(sponsors)
            
            # Log the action
            log_user_action(user, 'Created ordinance', ordinance)
            
            logger.info(f"Ordinance {ordinance.ordinance_number} created by {user}")
            
            return ordinance, []
            
        except ValidationError as e:
            errors.extend(e.messages if hasattr(e, 'messages') else [str(e)])
            return None, errors
        except Exception as e:
            logger.error(f"Error creating ordinance: {e}")
            errors.append("An unexpected error occurred")
            return None, errors
    
    @staticmethod
    def update_ordinance(ordinance: Ordinance, data: Dict[str, Any], user) -> List[str]:
        """
        Update an existing ordinance.
        
        Args:
            ordinance: Ordinance to update
            data: Updated data
            user: User performing the update
            
        Returns:
            List of errors (empty if successful)
        """
        errors = []
        
        try:
            # Track changes for logging
            changes = []
            
            # Update fields
            fields_to_update = ['ordinance_number', 'title', 'content', 'year_passed', 'status']
            for field in fields_to_update:
                if field in data:
                    old_value = getattr(ordinance, field)
                    new_value = data[field]
                    
                    if field == 'year_passed':
                        new_value = int(new_value)
                    
                    if old_value != new_value:
                        setattr(ordinance, field, new_value)
                        changes.append(f"{field}: {old_value} → {new_value}")
            
            # Update category
            if 'category_id' in data:
                old_category = ordinance.category
                if data['category_id']:
                    try:
                        new_category = Category.objects.get(id=data['category_id'])
                        ordinance.category = new_category
                        if old_category != new_category:
                            changes.append(f"category: {old_category} → {new_category}")
                    except Category.DoesNotExist:
                        errors.append("Invalid category selected")
                else:
                    ordinance.category = None
                    if old_category:
                        changes.append(f"category: {old_category} → None")
            
            if errors:
                return errors
            
            # Update user
            ordinance.updated_by = user
            
            # Save changes
            ordinance.full_clean()
            ordinance.save()
            
            # Update sponsors
            if 'sponsor_ids' in data:
                sponsors = Sponsor.objects.filter(id__in=data['sponsor_ids'])
                ordinance.sponsors.set(sponsors)
            
            # Log changes
            if changes:
                log_user_action(
                    user, 
                    'Updated ordinance', 
                    ordinance,
                    notes=f"Changes: {', '.join(changes)}"
                )
            
            logger.info(f"Ordinance {ordinance.ordinance_number} updated by {user}")
            
            return []
            
        except ValidationError as e:
            errors.extend(e.messages if hasattr(e, 'messages') else [str(e)])
            return errors
        except Exception as e:
            logger.error(f"Error updating ordinance: {e}")
            errors.append("An unexpected error occurred")
            return errors
    
    @staticmethod
    def update_ordinance_status(ordinance: Ordinance, new_status: str, user) -> bool:
        """
        Update ordinance status with validation and logging.
        
        Args:
            ordinance: Ordinance to update
            new_status: New status value
            user: User performing the update
            
        Returns:
            True if successful, False otherwise
        """
        try:
            valid_statuses = [choice[0] for choice in Ordinance.STATUS_CHOICES]
            if new_status not in valid_statuses:
                return False
            
            old_status = ordinance.status
            ordinance.status = new_status
            ordinance.updated_by = user
            ordinance.save()
            
            # Log the status change
            log_user_action(
                user,
                f'Status changed from {old_status} to {new_status}',
                ordinance,
                notes='Status updated via admin interface'
            )
            
            logger.info(f"Ordinance {ordinance.ordinance_number} status changed to {new_status} by {user}")
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating ordinance status: {e}")
            return False


class CategoryService:
    """Service class for category-related operations."""
    
    @staticmethod
    def get_categories_with_counts() -> QuerySet:
        """Get categories with ordinance counts."""
        cache_key = "categories_with_counts"
        categories = cache.get(cache_key)
        
        if categories is None:
            categories = Category.objects.with_ordinance_counts().order_by('name')
            cache.set(cache_key, categories, 300)  # Cache for 5 minutes
        
        return categories
    
    @staticmethod
    def get_active_categories() -> QuerySet:
        """Get categories that have at least one public ordinance."""
        return Category.objects.active().order_by('name')


class DashboardService:
    """Service class for dashboard-related operations."""
    
    @staticmethod
    def get_dashboard_data(user) -> Dict[str, Any]:
        """
        Get comprehensive dashboard data.
        
        Args:
            user: User requesting the dashboard
            
        Returns:
            Dictionary with dashboard data
        """
        cache_key = f"dashboard_data_{user.id if user.is_authenticated else 'anonymous'}"
        data = cache.get(cache_key)
        
        if data is None:
            # Get basic statistics
            stats = get_ordinance_statistics()
            
            # Get recent ordinances
            recent_ordinances = Ordinance.objects.with_related().order_by('-created_at')[:10]
            
            # Get category statistics
            category_stats = Category.objects.with_ordinance_counts().order_by('-total_ordinances')
            
            # Get top sponsors
            top_sponsors = Sponsor.objects.annotate(
                ordinance_count=Count('ordinance', filter=Q(ordinance__status__in=['approved', 'published']))
            ).filter(ordinance_count__gt=0).order_by('-ordinance_count')[:5]
            
            # Get recent activity logs
            recent_logs = OrdinanceLog.objects.select_related('ordinance', 'user').order_by('-timestamp')[:10]
            
            # Get officials
            officials = {
                'mayor': Official.objects.filter(position='mayor', status='active').first(),
                'vice_mayor': Official.objects.filter(position='vice_mayor', status='active').first(),
                'council_members': Official.objects.filter(
                    position='councilor', 
                    status='active'
                ).order_by('order', 'name')[:4]
            }
            
            data = {
                'stats': stats,
                'recent_ordinances': recent_ordinances,
                'category_stats': category_stats,
                'top_sponsors': top_sponsors,
                'recent_logs': recent_logs,
                'officials': officials,
            }
            
            # Cache for 5 minutes
            cache.set(cache_key, data, 300)
        
        return data


class SearchService:
    """Service class for search-related operations."""
    
    @staticmethod
    def get_search_suggestions(query: str, limit: int = 5) -> List[Dict[str, str]]:
        """
        Get search suggestions for autocomplete.
        
        Args:
            query: Search query
            limit: Maximum number of suggestions
            
        Returns:
            List of suggestion dictionaries
        """
        if len(query) < 2:
            return []
        
        sanitized_query = sanitize_search_query(query)
        
        ordinances = Ordinance.objects.public().filter(
            Q(title__icontains=sanitized_query) | 
            Q(ordinance_number__icontains=sanitized_query)
        ).values('title', 'ordinance_number', 'slug')[:limit]
        
        suggestions = [
            {
                'title': ord['title'],
                'ordinance_number': ord['ordinance_number'],
                'url': f"/ordinances/{ord['slug']}/"
            }
            for ord in ordinances
        ]
        
        return suggestions
