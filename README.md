# Sangguniang Bayan Ordinance System

🏛️ A comprehensive, secure, and optimized web application for managing and accessing municipal ordinances, built with Django, HTMX, Alpine.js, and TailwindCSS.

## 🚀 Recent Optimizations

This codebase has been significantly optimized for:
- **Security**: Environment variables, input validation, CSRF protection
- **Performance**: Database query optimization, caching, indexes
- **Code Quality**: Type hints, service layer, comprehensive testing
- **Maintainability**: Better error handling, logging, documentation
- **Production Readiness**: Docker, Nginx, monitoring

## Features

### Public Features
- **Browse Ordinances**: View all approved and published ordinances
- **Advanced Search**: Search by keyword, category, year, and status
- **Responsive Design**: Mobile-friendly interface using TailwindCSS
- **PDF Export**: Download ordinances as PDF files
- **Real-time Filtering**: HTMX-powered search and filtering without page reloads

### Admin Features
- **Full CRUD Operations**: Create, read, update, and delete ordinances
- **Status Management**: Change ordinance status (Draft → Reviewed → Approved → Published)
- **Category Management**: Organize ordinances by categories
- **Sponsor Management**: Track ordinance sponsors and their positions
- **File Attachments**: Upload and manage ordinance attachments
- **Activity Logging**: Track all changes and actions
- **Admin Dashboard**: Overview of system statistics

## Technology Stack

- **Backend**: Django 4.2
- **Frontend**: TailwindCSS (CDN), HTMX, Alpine.js
- **Database**: SQLite (development) / PostgreSQL (production)
- **PDF Generation**: ReportLab
- **File Handling**: Django's built-in file handling

## 🛠️ Installation

### Quick Start (Development)

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd sbo_system
   ```

2. **Set up environment**
   ```bash
   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate

   # Copy environment file
   cp .env.example .env
   # Edit .env with your settings
   ```

3. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

4. **Set up database**
   ```bash
   python manage.py migrate
   python manage.py createsuperuser
   python manage.py populate_sample_data  # Optional sample data
   ```

5. **Start development server**
   ```bash
   python manage.py runserver
   ```

6. **Access the application**
   - Public interface: http://127.0.0.1:8000/
   - Admin interface: http://127.0.0.1:8000/admin/
   - Admin dashboard: http://127.0.0.1:8000/admin-dashboard/

### 🐳 Docker Installation (Recommended for Production)

1. **Using Docker Compose**
   ```bash
   # Clone repository
   git clone <repository-url>
   cd sbo_system

   # Set up environment
   cp .env.example .env
   # Edit .env with production settings

   # Build and start services
   docker-compose up -d

   # Run migrations
   docker-compose exec web python manage.py migrate
   docker-compose exec web python manage.py createsuperuser
   ```

2. **Access the application**
   - Application: https://localhost (with SSL)
   - Admin: https://localhost/admin/

## Usage

### For the Public

1. **Browse Ordinances**: Visit the home page and click "Browse Ordinances"
2. **Search**: Use the search bar to find specific ordinances
3. **Filter**: Use the advanced filters to narrow down results by category, year, or status
4. **View Details**: Click on any ordinance to view its full content
5. **Download PDF**: Click the "Download PDF" button on any ordinance detail page

### For Administrators

1. **Login**: Access the admin interface at `/admin/` or click "Admin Login"
2. **Dashboard**: View system statistics at `/admin-dashboard/`
3. **Manage Ordinances**:
   - Create new ordinances through Django admin or the dashboard
   - Edit existing ordinances
   - Change status using the quick status update feature
   - Upload attachments
4. **Manage Categories**: Add and organize ordinance categories
5. **Manage Sponsors**: Add council members and their positions

## Project Structure

```
sbo_system/
├── sbo_system/           # Django project settings
├── ordinances/           # Main application
│   ├── models.py         # Database models
│   ├── views.py          # View functions
│   ├── admin.py          # Admin configuration
│   ├── urls.py           # URL routing
│   └── management/       # Management commands
├── templates/            # HTML templates
│   ├── base.html         # Base template
│   └── ordinances/       # App-specific templates
├── static/               # Static files (if any)
├── media/                # User uploads
└── requirements.txt      # Python dependencies
```

## Models

### Ordinance
- Ordinance number, title, content
- Category, year passed, status
- Sponsors (many-to-many relationship)
- Created/updated timestamps and users
- Slug for SEO-friendly URLs

### Category
- Name and slug for organizing ordinances

### Sponsor
- Name and position of council members

### Attachment
- File uploads related to ordinances
- Description and upload timestamp

### OrdinanceLog
- Activity logging for audit trail
- Tracks all changes and actions

## API Endpoints

### Public URLs
- `/` - Home page
- `/ordinances/` - Ordinance list with search/filter
- `/ordinances/<slug>/` - Ordinance detail
- `/ordinances/<slug>/pdf/` - PDF export
- `/search-suggestions/` - AJAX search suggestions

### Admin URLs
- `/admin-dashboard/` - Admin dashboard
- `/admin/ordinances/` - Admin ordinance management
- `/admin/ordinances/<id>/status/` - HTMX status update

## Customization

### Styling
The application uses TailwindCSS via CDN. To customize:
1. Modify the TailwindCSS classes in templates
2. Add custom CSS in the `<style>` section of `base.html`
3. For production, consider using a local TailwindCSS build

### Adding Features
1. Extend models in `ordinances/models.py`
2. Create/update views in `ordinances/views.py`
3. Add URL patterns in `ordinances/urls.py`
4. Create templates in `templates/ordinances/`

## 🔒 Security Features

### Environment-Based Configuration
- Sensitive settings moved to environment variables
- Separate development/production configurations
- Secret key management

### Input Validation & Sanitization
- Model-level validation with custom validators
- Search query sanitization
- File upload validation and restrictions
- CSRF and XSS protection

### Security Headers
- Content Security Policy (CSP)
- HTTP Strict Transport Security (HSTS)
- X-Frame-Options, X-Content-Type-Options
- Rate limiting for admin and API endpoints

## ⚡ Performance Optimizations

### Database Optimizations
- Custom QuerySets with optimized queries
- Database indexes on frequently queried fields
- Query optimization with `select_related` and `prefetch_related`
- Database constraints for data integrity

### Caching Strategy
- Redis caching for frequently accessed data
- Template fragment caching
- Cache invalidation on model changes
- Cached dashboard statistics

### Code Quality Improvements
- Type hints throughout the codebase
- Service layer for business logic separation
- Comprehensive error handling and logging
- Custom utility functions for common operations

## 📊 Monitoring & Maintenance

### Database Maintenance
```bash
# Optimize database performance
python manage.py optimize_database --analyze --clean-logs

# Vacuum database (PostgreSQL)
python manage.py optimize_database --vacuum
```

### Health Checks
- Docker health checks
- Database connection monitoring
- Application performance metrics

### Logging
- Structured logging configuration
- Separate log files for different components
- User action audit trail

## 📝 Testing

### Running Tests
```bash
# Run all tests
pytest

# Run with coverage
pytest --cov=ordinances --cov-report=html

# Run specific test file
pytest ordinances/tests/test_models.py
```

### Code Quality
```bash
# Format code
black .

# Sort imports
isort .

# Lint code
flake8

# Type checking
mypy ordinances/
```

## 🚀 Production Deployment

### Environment Variables
Configure these environment variables for production:

```bash
# Security
SECRET_KEY=your-secret-key-here
DEBUG=False
ALLOWED_HOSTS=yourdomain.com,www.yourdomain.com

# Database
DB_ENGINE=django.db.backends.postgresql
DB_NAME=sbo_db
DB_USER=sbo_user
DB_PASSWORD=secure_password
DB_HOST=localhost
DB_PORT=5432

# Email
EMAIL_BACKEND=django.core.mail.backends.smtp.EmailBackend
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password
```

### SSL Configuration
1. Obtain SSL certificates (Let's Encrypt recommended)
2. Place certificates in `ssl/` directory
3. Update nginx configuration if needed

### Performance Tuning
1. **Database**: Use PostgreSQL for production
2. **Caching**: Configure Redis for caching
3. **Static Files**: Use CDN for static file serving
4. **Media Files**: Configure cloud storage (AWS S3, etc.)
5. **Monitoring**: Set up application monitoring (Sentry, etc.)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License.

## Support

For support or questions, please contact the development team or create an issue in the repository.
