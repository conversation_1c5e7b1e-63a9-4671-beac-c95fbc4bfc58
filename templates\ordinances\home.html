{% extends 'base.html' %}

{% block title %}Home - Sangguniang Bayan Ordinance System{% endblock %}

{% block extra_head %}
<!-- Three.js CDN -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
<!-- AOS Animation Library -->
<link href="https://unpkg.com/aos@2.3.1/dist/aos.css" rel="stylesheet">
<script src="https://unpkg.com/aos@2.3.1/dist/aos.js"></script>
<!-- Font Awesome for Icons -->
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

<style>
    .hero-canvas {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
    }
    .hero-content {
        position: relative;
        z-index: 2;
    }
    .floating-animation {
        animation: float 6s ease-in-out infinite;
    }
    @keyframes float {
        0%, 100% { transform: translateY(0px); }
        50% { transform: translateY(-20px); }
    }
    .gradient-text {
        background: linear-gradient(45deg, #3B82F6, #8B5CF6, #06B6D4);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
    }
    .glass-effect {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }
    .official-card {
        transition: all 0.3s ease;
        transform-style: preserve-3d;
    }
    .official-card:hover {
        transform: translateY(-10px) rotateY(5deg);
        box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    }
    .stats-counter {
        font-size: 3rem;
        font-weight: bold;
        color: #3B82F6;
    }
</style>
{% endblock %}

{% block content %}
<!-- Hero Section with Three.js Background -->
<div class="relative bg-gradient-to-br from-blue-900 via-purple-900 to-indigo-900 text-white overflow-hidden" style="min-height: 100vh;">
    <canvas id="hero-canvas" class="hero-canvas"></canvas>

    <div class="hero-content max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 flex items-center min-h-screen">
        <div class="w-full">
            <div class="text-center" data-aos="fade-up" data-aos-duration="1000">
                <!-- Official Logo -->
                <div class="floating-animation mb-8">
                    <div class="relative inline-block">
                        <div class="w-32 h-32 md:w-40 md:h-40 mx-auto mb-6 relative">
                            <img src="{% load static %}{% static 'img/dumingag-logo.png' %}"
                                 alt="Municipality of Dumingag Official Logo"
                                 class="w-full h-full object-contain drop-shadow-2xl filter brightness-110">
                            <div class="absolute inset-0 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-full animate-pulse"></div>
                        </div>
                        <!-- Decorative elements around logo -->
                        <div class="absolute -top-2 -left-2 w-4 h-4 bg-yellow-400 rounded-full animate-ping"></div>
                        <div class="absolute -top-2 -right-2 w-3 h-3 bg-blue-400 rounded-full animate-ping" style="animation-delay: 0.5s;"></div>
                        <div class="absolute -bottom-2 -left-2 w-3 h-3 bg-purple-400 rounded-full animate-ping" style="animation-delay: 1s;"></div>
                        <div class="absolute -bottom-2 -right-2 w-4 h-4 bg-green-400 rounded-full animate-ping" style="animation-delay: 1.5s;"></div>
                    </div>
                </div>

                <h1 class="text-5xl md:text-7xl font-bold mb-6" data-aos="fade-up" data-aos-delay="200">
                    <span class="gradient-text">Municipality of Dumingag</span><br>
                    <span class="text-blue-200">Sangguniang Bayan</span><br>
                    <span class="text-blue-300 text-3xl md:text-4xl">Ordinance System</span>
                </h1>

                <p class="text-xl md:text-2xl mb-8 text-blue-100 max-w-4xl mx-auto leading-relaxed" data-aos="fade-up" data-aos-delay="400">
                    <i class="fas fa-scroll mr-2"></i>
                    Access and search through our comprehensive collection of municipal ordinances.
                    Stay informed about local laws and regulations that shape our community.
                </p>

                <!-- Statistics Cards -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-12 max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="600">
                    <div class="glass-effect rounded-xl p-6 text-center">
                        <i class="fas fa-file-alt text-3xl text-blue-300 mb-2"></i>
                        <div class="stats-counter" data-count="{{ total_ordinances }}">0</div>
                        <div class="text-blue-200">Total Ordinances</div>
                    </div>
                    <div class="glass-effect rounded-xl p-6 text-center">
                        <i class="fas fa-tags text-3xl text-purple-300 mb-2"></i>
                        <div class="stats-counter" data-count="{{ total_categories }}">0</div>
                        <div class="text-purple-200">Categories</div>
                    </div>
                    <div class="glass-effect rounded-xl p-6 text-center">
                        <i class="fas fa-users text-3xl text-green-300 mb-2"></i>
                        <div class="stats-counter" data-count="{{ top_sponsors.count }}">0</div>
                        <div class="text-green-200">Council Members</div>
                    </div>
                </div>

                <div class="flex flex-col sm:flex-row gap-4 justify-center" data-aos="fade-up" data-aos-delay="800">
                    <a href="{% url 'ordinances:ordinance_list' %}"
                       class="bg-white text-blue-900 px-8 py-4 rounded-xl font-semibold hover:bg-blue-50 transition-all duration-300 transform hover:scale-105 shadow-lg">
                        <i class="fas fa-search mr-2"></i>Browse Ordinances
                    </a>
                    <a href="#officials"
                       class="glass-effect text-white px-8 py-4 rounded-xl font-semibold hover:bg-white hover:bg-opacity-20 transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-user-tie mr-2"></i>Meet Our Officials
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Scroll Indicator -->
    <div class="absolute bottom-8 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
        <i class="fas fa-chevron-down text-2xl"></i>
    </div>
</div>

<!-- Officials Section -->
<section id="officials" class="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-16" data-aos="fade-up">
            <!-- Section Header with Logo -->
            <div class="flex items-center justify-center mb-6">
                {% comment %} <div class="w-16 h-16 mr-4">
                    <img src="{% load static %}{% static 'img/dumingag-logo.png' %}"
                         alt="Dumingag Logo"
                         class="w-full h-full object-contain opacity-80">
                </div> {% endcomment %}
                <h2 class="text-4xl font-bold text-gray-900">
                    Our Municipal Leaders
                </h2>
                {% comment %} <div class="w-16 h-16 ml-4">
                    <img src="{% load static %}{% static 'img/dumingag-logo.png' %}"
                         alt="Dumingag Logo"
                         class="w-full h-full object-contain opacity-80">
                </div> {% endcomment %}
            </div>
            <p class="text-xl text-gray-600 max-w-3xl mx-auto">
                Meet the dedicated officials serving the Municipality of Dumingag with integrity and commitment to public service.
            </p>
        </div>

        <!-- Mayor and Vice Mayor -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 mb-16">
            <!-- Mayor -->
            {% if officials.mayor %}
                <div class="official-card bg-white rounded-2xl shadow-xl overflow-hidden" data-aos="fade-right" data-aos-delay="200">
                    <div class="relative">
                        <div class="h-64 bg-gradient-to-br from-blue-600 to-purple-600 flex items-center justify-center">
                            <!-- Logo Watermark -->
                            <div class="absolute inset-0 flex items-center justify-center opacity-10">
                                <img src="{% load static %}{% static 'img/dumingag-logo.png' %}"
                                     alt="Dumingag Logo"
                                     class="w-48 h-48 object-contain">
                            </div>
                            <div class="w-32 h-32 bg-white rounded-full flex items-center justify-center relative z-10 overflow-hidden">
                                {% if officials.mayor.profile_picture %}
                                    <img src="{{ officials.mayor.profile_picture.url }}"
                                         alt="{{ officials.mayor.name }}"
                                         class="w-full h-full object-cover">
                                {% else %}
                                    <i class="fas fa-user-tie text-6xl text-blue-600"></i>
                                {% endif %}
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 bg-yellow-400 text-yellow-900 px-3 py-1 rounded-full text-sm font-semibold">
                            <i class="fas fa-crown mr-1"></i>Mayor
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ officials.mayor.name }}</h3>
                        <p class="text-blue-600 font-semibold mb-4">{{ officials.mayor.get_position_display }}</p>
                        {% if officials.mayor.bio %}
                            <p class="text-gray-600 mb-6">{{ officials.mayor.bio }}</p>
                        {% endif %}
                        {% if officials.mayor.get_achievements_list %}
                            <div class="space-y-2">
                                <h4 class="font-semibold text-gray-900">Key Achievements:</h4>
                                {% for achievement in officials.mayor.get_achievements_list %}
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                        {{ achievement }}
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% else %}
                <div class="bg-gray-100 rounded-2xl shadow-xl p-8 text-center" data-aos="fade-right" data-aos-delay="200">
                    <i class="fas fa-user-plus text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-600 mb-2">Mayor Position</h3>
                    <p class="text-gray-500">No mayor information available</p>
                </div>
            {% endif %}

            <!-- Vice Mayor -->
            {% if officials.vice_mayor %}
                <div class="official-card bg-white rounded-2xl shadow-xl overflow-hidden" data-aos="fade-left" data-aos-delay="400">
                    <div class="relative">
                        <div class="h-64 bg-gradient-to-br from-green-600 to-teal-600 flex items-center justify-center">
                            <!-- Logo Watermark -->
                            <div class="absolute inset-0 flex items-center justify-center opacity-10">
                                <img src="{% load static %}{% static 'img/dumingag-logo.png' %}"
                                     alt="Dumingag Logo"
                                     class="w-48 h-48 object-contain">
                            </div>
                            <div class="w-32 h-32 bg-white rounded-full flex items-center justify-center relative z-10 overflow-hidden">
                                {% if officials.vice_mayor.profile_picture %}
                                    <img src="{{ officials.vice_mayor.profile_picture.url }}"
                                         alt="{{ officials.vice_mayor.name }}"
                                         class="w-full h-full object-cover">
                                {% else %}
                                    <i class="fas fa-user-tie text-6xl text-green-600"></i>
                                {% endif %}
                            </div>
                        </div>
                        <div class="absolute top-4 right-4 bg-green-400 text-green-900 px-3 py-1 rounded-full text-sm font-semibold">
                            <i class="fas fa-star mr-1"></i>Vice Mayor
                        </div>
                    </div>
                    <div class="p-8">
                        <h3 class="text-2xl font-bold text-gray-900 mb-2">{{ officials.vice_mayor.name }}</h3>
                        <p class="text-green-600 font-semibold mb-4">{{ officials.vice_mayor.get_position_display }}</p>
                        {% if officials.vice_mayor.bio %}
                            <p class="text-gray-600 mb-6">{{ officials.vice_mayor.bio }}</p>
                        {% endif %}
                        {% if officials.vice_mayor.get_achievements_list %}
                            <div class="space-y-2">
                                <h4 class="font-semibold text-gray-900">Key Achievements:</h4>
                                {% for achievement in officials.vice_mayor.get_achievements_list %}
                                    <div class="flex items-center text-sm text-gray-600">
                                        <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                        {{ achievement }}
                                    </div>
                                {% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            {% else %}
                <div class="bg-gray-100 rounded-2xl shadow-xl p-8 text-center" data-aos="fade-left" data-aos-delay="400">
                    <i class="fas fa-user-plus text-4xl text-gray-400 mb-4"></i>
                    <h3 class="text-xl font-bold text-gray-600 mb-2">Vice Mayor Position</h3>
                    <p class="text-gray-500">No vice mayor information available</p>
                </div>
            {% endif %}
        </div>

        <!-- Council Members -->
        <div class="mb-12" data-aos="fade-up" data-aos-delay="600">
            <h3 class="text-3xl font-bold text-center text-gray-900 mb-8">
                <i class="fas fa-users text-purple-600 mr-3"></i>
                Sangguniang Bayan Members
            </h3>
            {% if officials.council_members %}
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {% for member in officials.council_members %}
                        <div class="official-card bg-white rounded-xl shadow-lg overflow-hidden" data-aos="zoom-in" data-aos-delay="{{ forloop.counter|add:600 }}">
                            <div class="h-48 bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center">
                                <div class="w-20 h-20 bg-white rounded-full flex items-center justify-center overflow-hidden">
                                    {% if member.profile_picture %}
                                        <img src="{{ member.profile_picture.url }}"
                                             alt="{{ member.name }}"
                                             class="w-full h-full object-cover">
                                    {% else %}
                                        <i class="fas fa-user text-4xl text-purple-600"></i>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="p-6">
                                <h4 class="text-lg font-bold text-gray-900 mb-1">{{ member.name }}</h4>
                                <p class="text-purple-600 text-sm font-semibold mb-2">{{ member.get_position_display }}</p>
                                {% if member.committee %}
                                    <p class="text-gray-600 text-sm">{{ member.committee }}</p>
                                {% endif %}
                            </div>
                        </div>
                    {% endfor %}
                </div>
            {% else %}
                <div class="text-center py-12">
                    <i class="fas fa-users text-6xl text-gray-300 mb-4"></i>
                    <h4 class="text-xl font-bold text-gray-600 mb-2">No Council Members</h4>
                    <p class="text-gray-500">Council member information will be displayed here when available.</p>
                </div>
            {% endif %}
        </div>
    </div>
</section>

<!-- Search Section -->
<div id="search" class="bg-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12" data-aos="fade-up">
            <h2 class="text-4xl font-bold text-gray-900 mb-4">
                <i class="fas fa-search text-blue-600 mr-3"></i>
                Search Ordinances
            </h2>
            <p class="text-xl text-gray-600">Find specific ordinances by keyword, number, or category</p>
        </div>

        <div class="max-w-4xl mx-auto" data-aos="fade-up" data-aos-delay="200">
            <form action="{% url 'ordinances:ordinance_list' %}" method="get" class="space-y-6">
                <!-- Main Search -->
                <div class="relative">
                    <input type="text"
                           name="search"
                           placeholder="Search by title, content, or ordinance number..."
                           class="w-full px-6 py-4 text-lg border-2 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 transition-all duration-300">
                    <button type="submit"
                            class="absolute right-2 top-2 bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-all duration-300 transform hover:scale-105">
                        <i class="fas fa-search mr-2"></i>Search
                    </button>
                </div>

                <!-- Enhanced Filters -->
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div class="relative">
                        <i class="fas fa-tags absolute left-3 top-4 text-gray-400"></i>
                        <select name="category" class="w-full pl-10 pr-4 py-3 border-2 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white">
                            <option value="">All Categories</option>
                            {% for category in categories %}
                                <option value="{{ category.slug }}">{{ category.name }}</option>
                            {% endfor %}
                        </select>
                    </div>

                    <div class="relative">
                        <i class="fas fa-calendar absolute left-3 top-4 text-gray-400"></i>
                        <select name="year" class="w-full pl-10 pr-4 py-3 border-2 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white">
                            <option value="">All Years</option>
                            <option value="2024">2024</option>
                            <option value="2023">2023</option>
                            <option value="2022">2022</option>
                            <option value="2021">2021</option>
                            <option value="2020">2020</option>
                        </select>
                    </div>

                    <div class="relative">
                        <i class="fas fa-flag absolute left-3 top-4 text-gray-400"></i>
                        <select name="status" class="w-full pl-10 pr-4 py-3 border-2 border-gray-300 rounded-xl focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none bg-white">
                            <option value="">All Status</option>
                            <option value="approved">Approved</option>
                            <option value="published">Published</option>
                        </select>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Recent Ordinances -->
<div class="bg-gray-50 py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">Recent Ordinances</h2>
            <p class="text-lg text-gray-600">Latest approved and published ordinances</p>
        </div>

        {% if recent_ordinances %}
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {% for ordinance in recent_ordinances %}
                    <div class="bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow">
                        <div class="p-6">
                            <div class="flex items-center justify-between mb-4">
                                <span class="bg-blue-100 text-blue-800 text-xs font-semibold px-2.5 py-0.5 rounded">
                                    {{ ordinance.ordinance_number }}
                                </span>
                                <span class="text-sm text-gray-500">{{ ordinance.year_passed }}</span>
                            </div>

                            <h3 class="text-lg font-semibold text-gray-900 mb-3 line-clamp-2">
                                {{ ordinance.title }}
                            </h3>

                            <p class="text-gray-600 text-sm mb-4 line-clamp-3">
                                {{ ordinance.content|truncatewords:20 }}
                            </p>

                            <div class="flex items-center justify-between">
                                <span class="text-xs text-gray-500">
                                    {% if ordinance.category %}{{ ordinance.category.name }}{% endif %}
                                </span>
                                <a href="{{ ordinance.get_absolute_url }}"
                                   class="text-blue-600 hover:text-blue-800 font-medium text-sm">
                                    Read More →
                                </a>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            <div class="text-center mt-12">
                <a href="{% url 'ordinances:ordinance_list' %}"
                   class="bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors">
                    View All Ordinances
                </a>
            </div>
        {% else %}
            <div class="text-center py-12">
                <div class="text-gray-400 mb-4">
                    <svg class="mx-auto h-16 w-16" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-2">No ordinances available</h3>
                <p class="text-gray-600">Check back later for new ordinances.</p>
            </div>
        {% endif %}
    </div>
</div>

<!-- Features Section -->
<div class="bg-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center mb-12">
            <h2 class="text-3xl font-bold text-gray-900 mb-4">System Features</h2>
            <p class="text-lg text-gray-600">Everything you need to access municipal ordinances</p>
        </div>

        <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
                <div class="bg-blue-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <svg class="h-8 w-8 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Advanced Search</h3>
                <p class="text-gray-600">Search by keywords, categories, years, and status with instant results.</p>
            </div>

            <div class="text-center">
                <div class="bg-green-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <svg class="h-8 w-8 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">PDF Export</h3>
                <p class="text-gray-600">Download ordinances as PDF files for offline reading and archival.</p>
            </div>

            <div class="text-center">
                <div class="bg-purple-100 rounded-full p-4 w-16 h-16 mx-auto mb-4 flex items-center justify-center">
                    <svg class="h-8 w-8 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                              d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                    </svg>
                </div>
                <h3 class="text-xl font-semibold text-gray-900 mb-2">Admin Management</h3>
                <p class="text-gray-600">Comprehensive admin interface for managing ordinances and attachments.</p>
            </div>
        </div>
    </div>
</div>

<!-- Official Footer Section -->
<section class="bg-gradient-to-r from-blue-900 via-purple-900 to-indigo-900 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="text-center" data-aos="fade-up">
            <!-- Official Seal and Information -->
            <div class="flex flex-col md:flex-row items-center justify-center mb-8">
                <div class="w-24 h-24 md:w-32 md:h-32 mb-6 md:mb-0 md:mr-8">
                    <img src="{% load static %}{% static 'img/dumingag-logo.png' %}"
                         alt="Municipality of Dumingag Official Seal"
                         class="w-full h-full object-contain drop-shadow-lg">
                </div>
                <div class="text-center md:text-left">
                    <h3 class="text-2xl md:text-3xl font-bold mb-2">Municipality of Dumingag</h3>
                    <p class="text-blue-200 text-lg mb-1">Province of Zamboanga del Sur</p>
                    <p class="text-blue-300 text-sm">Republic of the Philippines</p>
                </div>
            </div>

            <!-- Mission Statement -->
            <div class="max-w-4xl mx-auto mb-8">
                <p class="text-lg text-blue-100 leading-relaxed">
                    Committed to transparent governance and public service excellence,
                    the Municipality of Dumingag strives to build a progressive,
                    sustainable, and inclusive community for all residents.
                </p>
            </div>

            <!-- Contact Information -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-8 mb-8">
                <div class="text-center">
                    <i class="fas fa-map-marker-alt text-2xl text-blue-300 mb-2"></i>
                    <h4 class="font-semibold mb-1">Municipal Hall</h4>
                    <p class="text-blue-200 text-sm">Dumingag, Zamboanga del Sur</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-phone text-2xl text-blue-300 mb-2"></i>
                    <h4 class="font-semibold mb-1">Contact Number</h4>
                    <p class="text-blue-200 text-sm">(062) XXX-XXXX</p>
                </div>
                <div class="text-center">
                    <i class="fas fa-envelope text-2xl text-blue-300 mb-2"></i>
                    <h4 class="font-semibold mb-1">Email Address</h4>
                    <p class="text-blue-200 text-sm"><EMAIL></p>
                </div>
            </div>

            <!-- Copyright -->
            <div class="border-t border-blue-700 pt-6">
                <p class="text-blue-300 text-sm">
                    © {% now "Y" %} Municipality of Dumingag. All rights reserved. |
                    Powered by Sangguniang Bayan Ordinance System
                </p>
            </div>
        </div>
    </div>
</section>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize AOS (Animate On Scroll)
    AOS.init({
        duration: 1000,
        once: true,
        offset: 100
    });

    // Three.js Background Animation
    function initThreeJS() {
        const canvas = document.getElementById('hero-canvas');
        if (!canvas) return;

        const scene = new THREE.Scene();
        const camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
        const renderer = new THREE.WebGLRenderer({ canvas: canvas, alpha: true });

        renderer.setSize(window.innerWidth, window.innerHeight);
        renderer.setClearColor(0x000000, 0);

        // Create floating particles
        const particlesGeometry = new THREE.BufferGeometry();
        const particlesCount = 100;
        const posArray = new Float32Array(particlesCount * 3);

        for (let i = 0; i < particlesCount * 3; i++) {
            posArray[i] = (Math.random() - 0.5) * 10;
        }

        particlesGeometry.setAttribute('position', new THREE.BufferAttribute(posArray, 3));

        const particlesMaterial = new THREE.PointsMaterial({
            size: 0.02,
            color: 0x3B82F6,
            transparent: true,
            opacity: 0.6
        });

        const particlesMesh = new THREE.Points(particlesGeometry, particlesMaterial);
        scene.add(particlesMesh);

        // Create geometric shapes
        const geometries = [
            new THREE.BoxGeometry(0.5, 0.5, 0.5),
            new THREE.SphereGeometry(0.3, 8, 6),
            new THREE.ConeGeometry(0.3, 0.6, 6)
        ];

        const materials = [
            new THREE.MeshBasicMaterial({ color: 0x3B82F6, wireframe: true, transparent: true, opacity: 0.3 }),
            new THREE.MeshBasicMaterial({ color: 0x8B5CF6, wireframe: true, transparent: true, opacity: 0.3 }),
            new THREE.MeshBasicMaterial({ color: 0x06B6D4, wireframe: true, transparent: true, opacity: 0.3 })
        ];

        const meshes = [];
        for (let i = 0; i < 15; i++) {
            const geometry = geometries[Math.floor(Math.random() * geometries.length)];
            const material = materials[Math.floor(Math.random() * materials.length)];
            const mesh = new THREE.Mesh(geometry, material);

            mesh.position.x = (Math.random() - 0.5) * 20;
            mesh.position.y = (Math.random() - 0.5) * 20;
            mesh.position.z = (Math.random() - 0.5) * 20;

            mesh.rotation.x = Math.random() * Math.PI;
            mesh.rotation.y = Math.random() * Math.PI;

            scene.add(mesh);
            meshes.push(mesh);
        }

        camera.position.z = 5;

        // Animation loop
        function animate() {
            requestAnimationFrame(animate);

            // Rotate particles
            particlesMesh.rotation.x += 0.001;
            particlesMesh.rotation.y += 0.002;

            // Animate geometric shapes
            meshes.forEach((mesh, index) => {
                mesh.rotation.x += 0.01 + index * 0.001;
                mesh.rotation.y += 0.01 + index * 0.001;
                mesh.position.y += Math.sin(Date.now() * 0.001 + index) * 0.001;
            });

            renderer.render(scene, camera);
        }

        animate();

        // Handle window resize
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });
    }

    // Initialize Three.js
    initThreeJS();

    // Animated Counter
    function animateCounters() {
        const counters = document.querySelectorAll('.stats-counter');

        counters.forEach(counter => {
            const target = parseInt(counter.getAttribute('data-count'));
            const duration = 2000; // 2 seconds
            const increment = target / (duration / 16); // 60fps
            let current = 0;

            const timer = setInterval(() => {
                current += increment;
                if (current >= target) {
                    current = target;
                    clearInterval(timer);
                }
                counter.textContent = Math.floor(current);
            }, 16);
        });
    }

    // Trigger counter animation when hero section is visible
    const heroSection = document.querySelector('.hero-content');
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                animateCounters();
                observer.unobserve(entry.target);
            }
        });
    });

    if (heroSection) {
        observer.observe(heroSection);
    }

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Enhanced form interactions
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        searchInput.addEventListener('focus', function() {
            this.parentElement.classList.add('ring-2', 'ring-blue-500');
        });

        searchInput.addEventListener('blur', function() {
            this.parentElement.classList.remove('ring-2', 'ring-blue-500');
        });
    }

    // Add hover effects to official cards
    const officialCards = document.querySelectorAll('.official-card');
    officialCards.forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.transform = 'translateY(-10px) rotateY(5deg)';
        });

        card.addEventListener('mouseleave', function() {
            this.style.transform = 'translateY(0) rotateY(0)';
        });
    });

    // Parallax effect for scroll indicator
    const scrollIndicator = document.querySelector('.animate-bounce');
    if (scrollIndicator) {
        window.addEventListener('scroll', () => {
            const scrolled = window.pageYOffset;
            const opacity = Math.max(0, 1 - scrolled / 300);
            scrollIndicator.style.opacity = opacity;
        });
    }

    // Add loading animation to search button
    const searchForm = document.querySelector('form');
    if (searchForm) {
        searchForm.addEventListener('submit', function() {
            const button = this.querySelector('button[type="submit"]');
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Searching...';
            button.disabled = true;

            // Re-enable after a short delay (in case of quick redirects)
            setTimeout(() => {
                button.innerHTML = originalText;
                button.disabled = false;
            }, 3000);
        });
    }
});
</script>
{% endblock %}
{% endblock %}
